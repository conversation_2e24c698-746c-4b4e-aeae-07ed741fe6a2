# Production API Configuration
VITE_API_BASE_URL=http://community-workers:8000/api/v1

# Mock API Configuration (disabled in production)
VITE_MOCK_API=false
VITE_MOCK_DELAY=0

# Application Configuration
VITE_APP_TITLE=Community Services Admin
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=production

# Debug Configuration (disabled in production)
VITE_DEBUG=false
VITE_LOG_LEVEL=error

# Feature Flags
VITE_FEATURE_ANALYTICS=true
VITE_FEATURE_COMPETITIONS=true
VITE_FEATURE_CREDITS=true
VITE_FEATURE_SCHOOLS=true

# Performance Configuration
VITE_API_TIMEOUT=30000
VITE_RETRY_ATTEMPTS=3
