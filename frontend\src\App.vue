<template>
  <div id="app" class="app">
    <router-view />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useThemeStore } from '@/stores/theme'

const authStore = useAuthStore()
const themeStore = useThemeStore()

onMounted(() => {
  // Initialize authentication state from stored token
  authStore.initializeAuth()

  // Initialize theme system
  themeStore.initializeTheme()
})
</script>

<style lang="scss">
// CSS Custom Properties for Theme System
:root {
  // Light theme (default)
  --bg-color: #f5f5f5;
  --bg-color-secondary: #ffffff;
  --text-color: #2c3e50;
  --text-color-secondary: #7f8c8d;
  --text-color-light: #bfcbd9;
  --border-color: #e4e7ed;
  --sidebar-bg: #304156;
  --sidebar-text: #bfcbd9;
  --sidebar-active: #409EFF;
  --header-bg: #ffffff;
  --card-bg: #ffffff;
  --shadow-color: rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] {
  // Dark theme
  --bg-color: #1a1a1a;
  --bg-color-secondary: #2d2d2d;
  --text-color: #e4e7ed;
  --text-color-secondary: #a8abb2;
  --text-color-light: #6c6e72;
  --border-color: #4c4d4f;
  --sidebar-bg: #2d2d2d;
  --sidebar-text: #a8abb2;
  --sidebar-active: #409EFF;
  --header-bg: #363636;
  --card-bg: #363636;
  --shadow-color: rgba(0, 0, 0, 0.3);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

#app {
  height: 100vh;
  width: 100vw;
}

.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

// Element Plus customization
.el-button {
  font-weight: 500;
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 var(--shadow-color);
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

.el-form-item__label {
  font-weight: 500;
  color: var(--text-color);
}

// Utility classes
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mt-4 {
  margin-top: 1rem;
}

.p-4 {
  padding: 1rem;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-4 {
  gap: 1rem;
}
</style>
