"""
Community business logic and data access services.

Contains all business logic for community including data access,
event management, and member interactions.
"""

from cachetools import cached, TTLCache
from typing import List, Dict, Any, Optional
from datetime import datetime
from bson import ObjectId

from core.config import logger
from core.database.mongo_db import mongo_manager
from gateway.domains.community.schemas import (
    UniversityListResponse,
    MajorListResponse,
)
from libs.schemas.api.base import PaginationConfig
from libs.exceptions import ServiceException, MongoDBError
from libs.errors import CommunityDomainErrors

_service_cache = TTLCache(maxsize=512, ttl=60*60*12)

class CommunityServices:
    """Business logic services for community operations."""

    def __init__(self):
        """Initialize community services."""
        self.mongo_db = mongo_manager

    @cached(cache=_service_cache)
    async def get_universities_list(self, 
                                    country: Optional[str] = None,
                                    province: Optional[str] = None,
                                    database=None,
                                    limit: int = PaginationConfig.NO_LIMIT,
                                    offset: int = PaginationConfig.NO_OFFSET) -> UniversityListResponse:
        """Retrieve a list of all universities from the database."""
        logger.info("Fetching list of universities from database.")
        
        if database is None:
            raise ServiceException(
                code=400,
                errmsg=CommunityDomainErrors.database_error("Database Dependency Error"),
                data={"database": database, "collection": "universities"}
            )

        collection = database["universities"]
        query = {}
        if country:
            query["Country"] = country
        if province:
            query["Province"] = province
        
        try:

            if limit == PaginationConfig.NO_LIMIT and offset == PaginationConfig.NO_OFFSET:
                response = await collection.aggregate(
                    [
                        {"$match": query},
                        {"$project": {"_id": 0,"university_id": {"$toString": "$_id"},
                                      "University": 1, "Country": 1, "Province": 1}},
                    ]
                ).to_list(length=None)
            else:
                response = await collection.aggregate(
                    [
                        {"$match": query},
                        {"$skip": offset},
                        {"$limit": limit},
                        {"$project": {"_id": 0,"university_id": {"$toString": "$_id"},
                                      "University": 1, "Country": 1, "Province": 1}},
                    ]
                ).to_list(length=None)

            return UniversityListResponse(
                data=response,
                total=len(response),
                limit=limit, 
                offset=offset,
            )
        except Exception as e:
            logger.error(f"Database error fetching universities: {e}", exc_info=True)
            raise MongoDBError(
                operation="get_universities_list",
                message=CommunityDomainErrors.database_error(e),
                details={"database": database, "collection": "universities"},
                collection="universities",
            )
            
    @cached(cache=_service_cache)
    async def get_majors_list(self,
                              database=None,    
                              limit: int = PaginationConfig.NO_LIMIT,
                              offset: int = PaginationConfig.NO_OFFSET) -> MajorListResponse:
        """Retrieve a list of all majors from the database."""
        logger.info("Fetching list of majors from database.")
        if database is None:
            raise ServiceException(
                code=400,
                errmsg=CommunityDomainErrors.database_error("Database Dependency Error"),
                data={"database": database, "collection": "majors"}
            )
        collection = database["majors"]
    
        try:
            if limit == PaginationConfig.NO_LIMIT and offset == PaginationConfig.NO_OFFSET:
                response = await collection.aggregate(
                    [
                        {"$project": {"_id": 0,"major_id": {"$toString": "$_id"},
                                      "Major": 1, "Discipline": 1}},
                    ]
                ).to_list(length=None)
            else:
                response = await collection.aggregate(
                    [
                        {"$skip": offset},
                        {"$limit": limit},
                        {"$project": {"_id": 0,"major_id": {"$toString": "$_id"},
                                      "Major": 1, "Discipline": 1}},

                    ]   
                ).to_list(length=None)

            return MajorListResponse(
                data=response,
                total=len(response),
                limit=limit,
                offset=offset
            )
        except Exception as e:
            logger.error(f"Database error fetching majors: {e}", exc_info=True)
            raise MongoDBError(
                operation="get_majors_list",
                message=CommunityDomainErrors.database_error(e),
                details={"database": database, "collection": "majors"},
                collection="majors",
            )

    @cached(cache=_service_cache)
    async def get_content_creators(self, 
                                   start_date: str,
                                   database=None) -> List[Dict[str, Any]]:
        """Get content creators from MongoDB."""
        try:
            database = await self.mongo_db.get_database()

            start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
            start_object_id = ObjectId.from_datetime(start_datetime)

            pipeline = [
                {"$match": {"_id": {"$gte": start_object_id}}},
                {
                    "$project": {
                        "_id": 0,
                        "user_id": {"$toString": "$_id"},
                        "name": "$Name",
                        "email": "$Email",
                        "phone": "$Phone",
                        "university": "$University.University",
                        "identity": "$Identity",
                        "experience": "$Experience",
                        "join_date": {
                            "$dateToString": {
                                "format": "%Y-%m-%d %H:%M:%S",
                                "timezone": "Asia/Shanghai",
                                "date": "$JoinDate",
                            }
                        },
                    }
                },
            ]

            cursor = database["users"].aggregate(pipeline)
            creators = await cursor.to_list(length=None)

            logger.info(f"Retrieved {len(creators)} content creators")
            return creators

        except Exception as e:
            logger.error(f"Failed to get content creators: {e}")
            raise


# Global services instance
community_service = CommunityServices()
