/**
 * Mock Competition API Service
 */

import { initializeMockData } from './mockData.js'

// Initialize mock data
const mockData = initializeMockData()
let { competitions, users } = mockData

// Helper functions
const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms))

const paginate = (data, page = 1, limit = 20) => {
  const offset = (page - 1) * limit
  const paginatedData = data.slice(offset, offset + limit)
  
  return {
    data: paginatedData,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: data.length,
      pages: Math.ceil(data.length / limit),
      hasNext: offset + limit < data.length,
      hasPrev: page > 1
    }
  }
}

const filterCompetitions = (competitions, params) => {
  let filtered = [...competitions]
  
  if (params.status) {
    filtered = filtered.filter(comp => comp.status === params.status)
  }
  
  if (params.type) {
    filtered = filtered.filter(comp => comp.type === params.type)
  }
  
  if (params.category) {
    filtered = filtered.filter(comp => comp.category === params.category)
  }
  
  if (params.difficulty) {
    filtered = filtered.filter(comp => comp.difficulty === params.difficulty)
  }
  
  if (params.search) {
    const searchLower = params.search.toLowerCase()
    filtered = filtered.filter(comp => 
      comp.title.toLowerCase().includes(searchLower) ||
      comp.description.toLowerCase().includes(searchLower)
    )
  }
  
  if (params.featured !== undefined) {
    filtered = filtered.filter(comp => comp.featured === params.featured)
  }
  
  if (params.organizerId) {
    filtered = filtered.filter(comp => comp.organizer.id === params.organizerId)
  }
  
  if (params.tags && params.tags.length > 0) {
    filtered = filtered.filter(comp => 
      params.tags.some(tag => comp.tags.includes(tag))
    )
  }
  
  if (params.dateRange) {
    const { start, end } = params.dateRange
    if (start) {
      filtered = filtered.filter(comp => new Date(comp.startDate) >= new Date(start))
    }
    if (end) {
      filtered = filtered.filter(comp => new Date(comp.endDate) <= new Date(end))
    }
  }
  
  return filtered
}

// Mock API functions
export const competitionService = {
  // Get competitions list
  async getCompetitions(params = {}) {
    await delay()
    
    try {
      const filtered = filterCompetitions(competitions, params)
      const result = paginate(filtered, params.page, params.limit)
      
      return {
        success: true,
        ...result
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch competitions'
        }
      }
    }
  },

  // Get competition by ID
  async getCompetition(id) {
    await delay()
    
    try {
      const competition = competitions.find(comp => comp.id === id)
      
      if (!competition) {
        return {
          success: false,
          error: {
            code: 'COMPETITION_NOT_FOUND',
            message: 'Competition not found'
          }
        }
      }
      
      return {
        success: true,
        data: competition
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch competition'
        }
      }
    }
  },

  // Create competition
  async createCompetition(competitionData) {
    await delay()
    
    try {
      const newCompetition = {
        id: `comp_${competitions.length + 1}`,
        ...competitionData,
        currentParticipants: 0,
        stats: {
          totalParticipants: 0,
          totalTeams: 0,
          totalSubmissions: 0,
          averageScore: 0,
          completionRate: 0,
          participantsBySchool: {},
          participantsByLevel: {}
        },
        organizer: users[0], // Mock current user
        slug: competitionData.title.toLowerCase().replace(/\s+/g, '-'),
        viewCount: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      competitions.push(newCompetition)
      
      return {
        success: true,
        data: newCompetition,
        message: 'Competition created successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'CREATE_ERROR',
          message: 'Failed to create competition'
        }
      }
    }
  },

  // Update competition
  async updateCompetition(id, competitionData) {
    await delay()
    
    try {
      const index = competitions.findIndex(comp => comp.id === id)
      
      if (index === -1) {
        return {
          success: false,
          error: {
            code: 'COMPETITION_NOT_FOUND',
            message: 'Competition not found'
          }
        }
      }
      
      competitions[index] = {
        ...competitions[index],
        ...competitionData,
        updatedAt: new Date().toISOString()
      }
      
      return {
        success: true,
        data: competitions[index],
        message: 'Competition updated successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'UPDATE_ERROR',
          message: 'Failed to update competition'
        }
      }
    }
  },

  // Delete competition
  async deleteCompetition(id) {
    await delay()
    
    try {
      const index = competitions.findIndex(comp => comp.id === id)
      
      if (index === -1) {
        return {
          success: false,
          error: {
            code: 'COMPETITION_NOT_FOUND',
            message: 'Competition not found'
          }
        }
      }
      
      competitions.splice(index, 1)
      
      return {
        success: true,
        message: 'Competition deleted successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'DELETE_ERROR',
          message: 'Failed to delete competition'
        }
      }
    }
  },

  // Join competition
  async joinCompetition(competitionId, joinData) {
    await delay()
    
    try {
      const competition = competitions.find(comp => comp.id === competitionId)
      
      if (!competition) {
        return {
          success: false,
          error: {
            code: 'COMPETITION_NOT_FOUND',
            message: 'Competition not found'
          }
        }
      }
      
      if (competition.currentParticipants >= competition.maxParticipants) {
        return {
          success: false,
          error: {
            code: 'COMPETITION_FULL',
            message: 'Competition is full'
          }
        }
      }
      
      // Mock joining logic
      competition.currentParticipants += 1
      competition.stats.totalParticipants += 1
      
      return {
        success: true,
        message: 'Successfully joined competition'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'JOIN_ERROR',
          message: 'Failed to join competition'
        }
      }
    }
  },

  // Get competition statistics
  async getCompetitionStats() {
    await delay()
    
    try {
      const stats = {
        totalCompetitions: competitions.length,
        activeCompetitions: competitions.filter(c => c.status === 'active').length,
        upcomingCompetitions: competitions.filter(c => c.status === 'registration_open').length,
        completedCompetitions: competitions.filter(c => c.status === 'completed').length,
        totalParticipants: competitions.reduce((sum, c) => sum + c.currentParticipants, 0),
        averageParticipants: Math.round(competitions.reduce((sum, c) => sum + c.currentParticipants, 0) / competitions.length),
        popularCategories: competitions.reduce((acc, c) => {
          acc[c.category] = (acc[c.category] || 0) + 1
          return acc
        }, {}),
        monthlyTrend: [
          { month: '2024-01', competitions: 5, participants: 234 },
          { month: '2024-02', competitions: 8, participants: 456 },
          { month: '2024-03', competitions: 12, participants: 678 }
        ]
      }
      
      return {
        success: true,
        data: stats
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'STATS_ERROR',
          message: 'Failed to fetch statistics'
        }
      }
    }
  }
}
