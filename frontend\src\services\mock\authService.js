/**
 * Mock Authentication API Service
 */

import { initializeMockData } from './mockData.js'

// Initialize mock data
const mockData = initializeMockData()
let { users } = mockData

// Helper functions
const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms))

// Default admin test users (ADMIN-ONLY SYSTEM)
const defaultUsers = [
  {
    id: 'admin_1',
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123', // In real app, this would be hashed
    name: 'System Administrator',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
    role: 'admin',
    status: 'active',
    lastLoginAt: new Date().toISOString(),
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: new Date().toISOString()
  },
  {
    id: 'super_admin_1',
    username: 'superadmin',
    email: '<EMAIL>',
    password: 'superadmin123',
    name: 'Super Administrator',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=superadmin',
    role: 'super_admin',
    status: 'active',
    lastLoginAt: new Date().toISOString(),
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: new Date().toISOString()
  }
]

// Add default users to the beginning of the users array
users.unshift(...defaultUsers)

// Mock session storage
let currentSession = null

// Mock API functions
export const authService = {
  // Login
  async login(credentials) {
    await delay()
    
    try {
      const { email, password } = credentials
      
      // Find user by email
      const user = users.find(u => u.email === email)
      
      if (!user) {
        return {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found with this email address'
          }
        }
      }
      
      // Check password (in real app, this would be hashed comparison)
      if (user.password !== password) {
        return {
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: 'Invalid email or password'
          }
        }
      }
      
      // Check if user is active
      if (user.status !== 'active') {
        return {
          success: false,
          error: {
            code: 'ACCOUNT_INACTIVE',
            message: 'Your account is inactive. Please contact support.'
          }
        }
      }
      
      // Generate mock JWT token
      const token = `mock_jwt_${user.id}_${Date.now()}`
      
      // Update last login
      user.lastLoginAt = new Date().toISOString()
      
      // Create session
      currentSession = {
        token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          name: user.name,
          avatar: user.avatar,
          role: user.role,
          status: user.status,
          lastLoginAt: user.lastLoginAt
        },
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
      }
      
      return {
        success: true,
        data: {
          token,
          user: currentSession.user
        },
        message: 'Login successful'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'LOGIN_ERROR',
          message: 'An error occurred during login'
        }
      }
    }
  },

  // Register
  async register(userData) {
    await delay()
    
    try {
      const { email, password, name, username } = userData
      
      // Check if user already exists
      const existingUser = users.find(u => u.email === email || u.username === username)
      
      if (existingUser) {
        return {
          success: false,
          error: {
            code: 'USER_EXISTS',
            message: 'User with this email or username already exists'
          }
        }
      }
      
      // Create new user
      const newUser = {
        id: `user_${users.length + 1}`,
        username: username || email.split('@')[0],
        email,
        password, // In real app, this would be hashed
        name,
        avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${email}`,
        role: 'viewer', // Default role
        status: 'active',
        lastLoginAt: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      users.push(newUser)
      
      return {
        success: true,
        data: {
          user: {
            id: newUser.id,
            username: newUser.username,
            email: newUser.email,
            name: newUser.name,
            avatar: newUser.avatar,
            role: newUser.role,
            status: newUser.status
          }
        },
        message: 'Registration successful'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'REGISTRATION_ERROR',
          message: 'An error occurred during registration'
        }
      }
    }
  },

  // Get current user
  async me(token) {
    await delay()
    
    try {
      if (!currentSession || currentSession.token !== token) {
        return {
          success: false,
          error: {
            code: 'INVALID_TOKEN',
            message: 'Invalid or expired token'
          }
        }
      }
      
      // Check if session is expired
      if (new Date() > new Date(currentSession.expiresAt)) {
        currentSession = null
        return {
          success: false,
          error: {
            code: 'TOKEN_EXPIRED',
            message: 'Token has expired'
          }
        }
      }
      
      return {
        success: true,
        data: {
          user: currentSession.user
        }
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'AUTH_ERROR',
          message: 'Authentication error'
        }
      }
    }
  },

  // Logout
  async logout(token) {
    await delay()
    
    try {
      if (currentSession && currentSession.token === token) {
        currentSession = null
      }
      
      return {
        success: true,
        message: 'Logout successful'
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'LOGOUT_ERROR',
          message: 'An error occurred during logout'
        }
      }
    }
  },

  // Get default admin test credentials (ADMIN-ONLY SYSTEM)
  getTestCredentials() {
    return {
      admin: {
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin'
      },
      super_admin: {
        email: '<EMAIL>',
        password: 'superadmin123',
        role: 'super_admin'
      }
    }
  }
}
