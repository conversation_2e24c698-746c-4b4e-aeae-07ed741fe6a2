/**
 * Credit Service
 * Integrates with Camp Admin API for credit management
 * Replaces mock creditService with real API calls
 */

import { campAdminApi } from './api/campAdminApi.js'
import { analyticsApi } from './api/analyticsApi.js'
import { creditAdapter } from './adapters/creditAdapter.js'
import { analyticsAdapter } from './adapters/analyticsAdapter.js'
import { handleApiError, logApiCall, isMockApiEnabled } from '../utils/apiHelpers.js'

// Import mock service as fallback
import { creditService as mockCreditService } from './mock/creditService.js'

/**
 * Real Credit Service Implementation
 * Maps credit data from Camp Admin API to credit interface
 */
class CreditService {
  /**
   * Get credits list
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number
   * @param {number} params.limit - Items per page
   * @param {string} params.userId - Filter by user ID
   * @param {string} params.competitionId - Filter by competition ID
   * @param {string} params.qualificationId - Filter by qualification ID
   * @param {string} params.status - Filter by status
   * @param {string} params.type - Filter by type
   * @param {string} params.source - Filter by source
   * @param {string} params.search - Search term
   * @param {Object} params.dateRange - Date range filter
   * @param {string} params.sortBy - Sort field
   * @param {string} params.sortOrder - Sort order (asc/desc)
   * @returns {Promise<Object>} Credits list response
   */
  async getCredits(params = {}) {
    // Use mock service if enabled
    if (isMockApiEnabled()) {
      logApiCall('GET', '/credits (mock)', params, null)
      return mockCreditService.getCredits(params)
    }

    try {
      logApiCall('GET', '/camp-admin/credits/history', params, null)

      // Transform parameters for Camp Admin API
      const apiParams = {
        limit: params.limit || 200, // Camp Admin API default is 200
        offset: params.page ? (params.page - 1) * (params.limit || 200) : 0,
        competition_id: params.competitionId,
        user_id: params.userId
      }

      // Get credit history from Camp Admin API
      const creditsResponse = await campAdminApi.getCreditHistory(apiParams)

      if (!creditsResponse.success) {
        throw new Error(creditsResponse.error?.message || 'Failed to fetch credits')
      }

      // Transform credits to frontend format
      const transformedData = creditAdapter.transform(creditsResponse, {
        type: 'list',
        isPaginated: true
      })

      // Apply additional client-side filtering
      let credits = transformedData.data
      
      // Apply filters not supported by API
      if (params.qualificationId) {
        credits = credits.filter(credit => credit.qualificationId === params.qualificationId)
      }
      
      if (params.status) {
        credits = credits.filter(credit => credit.status === params.status)
      }
      
      if (params.type) {
        credits = credits.filter(credit => credit.type === params.type)
      }
      
      if (params.source) {
        credits = credits.filter(credit => credit.source === params.source)
      }
      
      if (params.search) {
        const searchLower = params.search.toLowerCase()
        credits = credits.filter(credit => 
          credit.userName.toLowerCase().includes(searchLower) ||
          credit.competitionName.toLowerCase().includes(searchLower) ||
          credit.qualificationName.toLowerCase().includes(searchLower) ||
          (credit.remark && credit.remark.toLowerCase().includes(searchLower))
        )
      }
      
      if (params.dateRange) {
        const { start, end } = params.dateRange
        if (start) {
          credits = credits.filter(credit => new Date(credit.awardedAt) >= new Date(start))
        }
        if (end) {
          credits = credits.filter(credit => new Date(credit.awardedAt) <= new Date(end))
        }
      }

      // Apply sorting
      if (params.sortBy) {
        credits = this.sortCredits(credits, params.sortBy, params.sortOrder)
      }

      // Create pagination info (client-side for filtered data)
      const total = credits.length
      const page = params.page || 1
      const limit = params.limit || 200
      const offset = (page - 1) * limit
      const paginatedData = credits.slice(offset, offset + limit)

      const result = {
        success: true,
        data: paginatedData,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: total,
          pages: Math.ceil(total / limit),
          hasNext: offset + limit < total,
          hasPrev: page > 1
        }
      }

      logApiCall('GET', '/camp-admin/credits/history', params, result)
      return result

    } catch (error) {
      const normalizedError = handleApiError(error)
      logApiCall('GET', '/camp-admin/credits/history', params, normalizedError)
      throw normalizedError
    }
  }

  /**
   * Get credit by ID
   * @param {string} id - Credit ID
   * @returns {Promise<Object>} Credit details response
   */
  async getCredit(id) {
    // Use mock service if enabled
    if (isMockApiEnabled()) {
      logApiCall('GET', `/credits/${id} (mock)`, null, null)
      return mockCreditService.getCredit(id)
    }

    try {
      logApiCall('GET', `/camp-admin/credits/${id}`, null, null)

      // Get all credits and find by ID (Camp Admin API doesn't have single credit endpoint)
      const creditsResponse = await campAdminApi.getCreditHistory({ limit: 1000 })

      if (!creditsResponse.success) {
        throw new Error(creditsResponse.error?.message || 'Failed to fetch credit')
      }

      const credit = creditsResponse.data.find(c => c.id === id)

      if (!credit) {
        return {
          success: false,
          error: {
            code: 'CREDIT_NOT_FOUND',
            message: 'Credit not found'
          }
        }
      }

      // Transform to detailed credit format
      const transformedCredit = creditAdapter.transformItem(credit, 'detail')

      const result = {
        success: true,
        data: transformedCredit
      }

      logApiCall('GET', `/camp-admin/credits/${id}`, null, result)
      return result

    } catch (error) {
      const normalizedError = handleApiError(error)
      logApiCall('GET', `/camp-admin/credits/${id}`, null, normalizedError)
      throw normalizedError
    }
  }

  /**
   * Award credits to users
   * @param {Object} awardData - Credit award data
   * @param {string[]} awardData.userIds - Array of user IDs
   * @param {string} awardData.competitionId - Competition ID
   * @param {string} awardData.qualificationId - Qualification ID
   * @param {number} awardData.credit - Credit amount
   * @param {string} awardData.remark - Award remark
   * @returns {Promise<Object>} Award response
   */
  async awardCredits(awardData) {
    // Use mock service if enabled (create equivalent)
    if (isMockApiEnabled()) {
      logApiCall('POST', '/credits/award (mock)', awardData, null)
      // Mock doesn't have award function, use create for single user
      if (awardData.userIds && awardData.userIds.length > 0) {
        return mockCreditService.createCredit({
          userId: awardData.userIds[0],
          amount: awardData.credit,
          reason: awardData.remark || 'Credit awarded',
          type: 'award',
          source: 'competition',
          status: 'issued'
        })
      }
    }

    try {
      logApiCall('POST', '/camp-admin/credits/award', awardData, null)

      // Transform award data for Camp Admin API
      const apiData = creditAdapter.transformAwardRequest(awardData)

      // Award credits via Camp Admin API
      const awardResponse = await campAdminApi.awardCredits(apiData)

      if (!awardResponse.success) {
        throw new Error(awardResponse.error?.message || 'Failed to award credits')
      }

      // Transform response
      const transformedResponse = creditAdapter.transformAwardResponse(awardResponse)

      logApiCall('POST', '/camp-admin/credits/award', awardData, transformedResponse)
      return transformedResponse

    } catch (error) {
      const normalizedError = handleApiError(error)
      logApiCall('POST', '/camp-admin/credits/award', awardData, normalizedError)
      throw normalizedError
    }
  }

  /**
   * Revoke credits
   * @param {Object} revokeData - Credit revoke data
   * @param {string} revokeData.recordId - Credit record ID to revoke
   * @returns {Promise<Object>} Revoke response
   */
  async revokeCredits(revokeData) {
    // Use mock service if enabled (not supported)
    if (isMockApiEnabled()) {
      logApiCall('POST', '/credits/revoke (mock)', revokeData, null)
      return {
        success: false,
        error: {
          code: 'NOT_SUPPORTED',
          message: 'Credit revocation not supported in mock mode'
        }
      }
    }

    try {
      logApiCall('POST', '/camp-admin/credits/revoke', revokeData, null)

      // Transform revoke data for Camp Admin API
      const apiData = creditAdapter.transformRevokeRequest(revokeData)

      // Revoke credits via Camp Admin API
      const revokeResponse = await campAdminApi.revokeCredits(apiData)

      if (!revokeResponse.success) {
        throw new Error(revokeResponse.error?.message || 'Failed to revoke credits')
      }

      // Transform response
      const transformedResponse = creditAdapter.transformRevokeResponse(revokeResponse)

      logApiCall('POST', '/camp-admin/credits/revoke', revokeData, transformedResponse)
      return transformedResponse

    } catch (error) {
      const normalizedError = handleApiError(error)
      logApiCall('POST', '/camp-admin/credits/revoke', revokeData, normalizedError)
      throw normalizedError
    }
  }

  /**
   * Get credit statistics
   * @returns {Promise<Object>} Credit statistics response
   */
  async getCreditStats() {
    // Use mock service if enabled
    if (isMockApiEnabled()) {
      logApiCall('GET', '/credits/stats (mock)', null, null)
      return mockCreditService.getCreditStats()
    }

    try {
      logApiCall('GET', '/credits/stats', null, null)

      // Get data from multiple sources
      const [creditsResponse, summaryStatsResponse] = await Promise.all([
        campAdminApi.getCreditHistory({ limit: 1000 }),
        analyticsApi.getSummaryStatistics()
      ])

      if (!creditsResponse.success) {
        throw new Error('Failed to fetch credits data')
      }

      // Transform analytics data
      const summaryStats = analyticsAdapter.transformSummaryStatistics(summaryStatsResponse.data || [])
      const credits = creditsResponse.data || []

      // Calculate statistics from credits data
      const totalIssued = credits.filter(c => c.status === 'active').length
      const totalPending = credits.filter(c => c.status === 'pending').length
      const totalRevoked = credits.filter(c => c.status === 'revoked').length

      // Credit amount statistics
      const activeCredits = credits.filter(c => c.status === 'active')
      const totalAmount = activeCredits.reduce((sum, c) => sum + (c.credit || 0), 0)
      const averageAmount = activeCredits.length > 0 ? totalAmount / activeCredits.length : 0

      // Distribution by competition
      const distributionByCompetition = credits.reduce((acc, c) => {
        const comp = c.competition_name || 'Unknown'
        acc[comp] = (acc[comp] || 0) + (c.credit || 0)
        return acc
      }, {})

      // Distribution by qualification
      const distributionByQualification = credits.reduce((acc, c) => {
        const qual = c.qualification_name || 'Unknown'
        acc[qual] = (acc[qual] || 0) + (c.credit || 0)
        return acc
      }, {})

      // Monthly trend (simplified - would need date grouping)
      const monthlyTrend = [
        { month: '2024-01', issued: Math.floor(totalIssued * 0.3), amount: Math.floor(totalAmount * 0.3) },
        { month: '2024-02', issued: Math.floor(totalIssued * 0.4), amount: Math.floor(totalAmount * 0.4) },
        { month: '2024-03', issued: Math.floor(totalIssued * 0.3), amount: Math.floor(totalAmount * 0.3) }
      ]

      const stats = {
        totalIssued,
        totalPending,
        totalRevoked,
        totalAmount,
        averageAmount: Math.round(averageAmount),
        distributionByCompetition,
        distributionByQualification,
        monthlyTrend,
        summary: summaryStats
      }

      const result = {
        success: true,
        data: stats
      }

      logApiCall('GET', '/credits/stats', null, result)
      return result

    } catch (error) {
      const normalizedError = handleApiError(error)
      logApiCall('GET', '/credits/stats', null, normalizedError)
      throw normalizedError
    }
  }

  /**
   * Create credit (alias for award credits with single user)
   * @param {Object} creditData - Credit data
   * @returns {Promise<Object>} Create response
   */
  async createCredit(creditData) {
    // Transform single credit to award format
    const awardData = {
      userIds: [creditData.userId],
      competitionId: creditData.competitionId,
      qualificationId: creditData.qualificationId,
      credit: creditData.amount || creditData.credit,
      remark: creditData.reason || creditData.remark || 'Credit awarded'
    }

    return this.awardCredits(awardData)
  }

  /**
   * Sort credits array
   * @param {Array} credits - Credits array
   * @param {string} sortBy - Sort field
   * @param {string} sortOrder - Sort order
   * @returns {Array} Sorted credits
   */
  sortCredits(credits, sortBy, sortOrder = 'asc') {
    return credits.sort((a, b) => {
      let aVal = a[sortBy]
      let bVal = b[sortBy]

      // Handle nested properties
      if (sortBy === 'amount') {
        aVal = a.credit || 0
        bVal = b.credit || 0
      }

      // Handle dates
      if (sortBy === 'awardedAt' || sortBy === 'issuedAt' || sortBy === 'createdAt') {
        aVal = new Date(aVal || 0)
        bVal = new Date(bVal || 0)
      }

      // Handle string comparison
      if (typeof aVal === 'string') {
        aVal = aVal.toLowerCase()
        bVal = bVal.toLowerCase()
      }

      // Sort logic
      if (sortOrder === 'desc') {
        return bVal > aVal ? 1 : -1
      }
      return aVal > bVal ? 1 : -1
    })
  }
}

// Create and export service instance
export const creditService = new CreditService()

// Export individual methods for convenience
export const {
  getCredits,
  getCredit,
  awardCredits,
  revokeCredits,
  getCreditStats,
  createCredit
} = creditService
