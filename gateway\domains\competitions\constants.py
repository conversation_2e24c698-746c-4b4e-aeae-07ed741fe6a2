"""
Constants for the competitions domain.

Contains competition-specific enums and constants including
qualification types and logic definitions.
"""

from enum import Enum
from libs.mongo.snippets import convert_utc_to_local

class CompetitionType(str, Enum):
    """Types of competition."""
    DATA_ANALYSIS = "DATA_ANALYSIS"
    ALGORITHM = "ALGORITHM"
    SCHEME = "SCHEME"
    TRAINING_CAMP = "TRAINING_CAMP"
    LIVE = "LIVE"
    OTHER = "OTHER"
    WORKSHOP = "WORKSHOP"
    TEST = "TEST"
    EXERCISE_DATA_ANALYSIS = "EXERCISE_DATA_ANALYSIS"
    EXERCISE_ALGORITHM = "EXERCISE_ALGORITHM"
    EXERCISE_SCHEME = "EXERCISE_SCHEME"
    EXERCISE = "EXERCISE"

COMPETITION_REGISTER_PROFILE_BASIC = {
                        "_id": 0,
                        "user_id": {"$toString": "$User"},
                        "register_ts": 1,
                        "Phone": 1,
                        "Email": {
                            "$arrayElemAt": [
                                {
                                    "$map": {
                                        "input": {
                                            "$filter": {
                                                "input": "$RegisterForm",
                                                "as": "item",
                                                "cond": {
                                                    "$eq": ["$$item.Name", "邮箱地址"]
                                                },
                                            }
                                        },
                                        "as": "filteredElement",
                                        "in": "$$filteredElement.Value",
                                    }
                                },
                                0,
                            ]
                        },
                        "competition_id": {"$toString": "$Competition"},
}

COMPETITION_REGISTER_PROFILE_DETAIL = {
    **COMPETITION_REGISTER_PROFILE_BASIC,
    "competition_type": "$comp_info.DetailType",
    "competition_name": "$comp_info.Name",
}

COMPETITION_REGISTER_PROFILE_FULL_FORM = {
    **COMPETITION_REGISTER_PROFILE_BASIC,
    "register_form": {
        "$arrayToObject": {
            "$map": {
                "input": "$RegisterForm",
                "as": "item",
                "in": {
                    "k": "$$item.Name",
                    "v": "$$item.Value"
                }
            }
        }
    },
}

__all__ = [
    "CompetitionType",
]
