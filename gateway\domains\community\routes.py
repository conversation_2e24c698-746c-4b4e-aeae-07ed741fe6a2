"""
Community Management API Router.

Handles all community-related endpoints organized by business function:
- Universities and majors listings
- User discovery by location, university, identity
- Content creator management
- Competition user registration and participation
- Community data search and filtering
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from typing import Optional
import logging

from gateway.domains.community.schemas import (
    UniversityListResponse,
    MajorListResponse,
)


from libs.auth.permissions import (
    require_user_data_access,
)
from libs.schemas.api.responses import success, ErrorResponse, SuccessResponse  
from libs.schemas.api.base import PaginationConfig
from core.database.dependencies import get_mongo_kesci_db

from .services import community_service

logger = logging.getLogger(__name__)

# Create router with prefix for community endpoints
router = APIRouter(
    prefix="/community",
    tags=["community"],
    responses={404: {"description": "Not found"}},
)



# ——— Universities and majors endpoints ———


@router.get(
    "/universities",
    response_model=SuccessResponse | ErrorResponse,
    summary="List all universities",
    description="Get a list of all universities in the platform",
)
async def list_universities(
    limit: int = Query(
        -1, le=1000, description="Number of universities to return"
    ),
    offset: int = Query(-1, description="Number of universities to skip"),
    user_id: str = Depends(require_user_data_access),
    database = Depends(get_mongo_kesci_db),       
    country: Optional[str] = Query(None, description="Filter by country"),
    province: Optional[str] = Query(None, description="Filter by province"),
):
    """List all available universities."""
    try:
        result:UniversityListResponse = await community_service.get_universities_list(
                                         database=database,
                                         limit=limit,
                                         offset=offset,
                                         country=country,
                                         province=province)

        return success(data=result.data)

    except Exception as e:
        logger.error(f"Error listing universities: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get(
    "/majors",
    response_model=SuccessResponse | ErrorResponse,
    summary="List all majors",
    description="Get a list of all majors across all universities",
)
async def list_majors(
    limit: int = Query(PaginationConfig.DEFAULT_LIMIT, ge=1, le=1000, description="Number of majors to return"),
    offset: int = Query(PaginationConfig.DEFAULT_OFFSET, ge=0, description="Number of majors to skip"),
    user_id: str = Depends(require_user_data_access),
    database = Depends(get_mongo_kesci_db),
):
    """List all available majors."""
    try:
        result:MajorListResponse = await community_service.get_majors_list(
                                         database=database,
                                         limit=limit,
                                         offset=offset)

        return success(data=result.data)

    except Exception as e:
        logger.error(f"Error listing majors: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))



# ——— Content creator endpoints ———

# @router.post(
#     "/content_creators",
#     response_model=SuccessResponse | ErrorResponse,
#     summary="Get content creators",
#     description="Fetch content creators based on start date criteria",
# )
# async def get_content_creators(
#     creators_data: ContentCreatorsRequest = Body(...), # type: ignore
#     user_id: str = Depends(require_content_creator_access),
#     database = Depends(get_database),
# ):
#     """Get content creators based on criteria."""
#     try:
#         # Validate date format before processing.
#         validate_date_format(creators_data.start_date)

#         # Execute content creator retrieval via community service.
#         result = await community_service.get_content_creators(
#                                          database=database,
#                                          creators_data=creators_data)

#         # Handle service layer errors with proper HTTP status codes.
#         if not result.success:
#             logger.error(
#                 f"Get content creators failed: {result.error}"
#             )
#             raise HTTPException(
#                 status_code=500,
#                 detail={
#                     "message": result.error,
#                 },
#             )

#         return success(data=result.data)

#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"Error getting content creators: {e}", exc_info=True)
#         raise HTTPException(status_code=500, detail=str(e))




