/**
 * Mock data generator for development
 */

import { faker } from '@faker-js/faker'

// Set seed for consistent data
faker.seed(123)

// Helper functions
const randomChoice = (array) => array[Math.floor(Math.random() * array.length)]
const randomChoices = (array, count) => {
  const shuffled = [...array].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}

// Generate users
export const generateUsers = (count = 100) => {
  const users = []
  const roles = ['admin', 'manager', 'operator', 'viewer']
  const statuses = ['active', 'inactive', 'suspended']
  
  for (let i = 0; i < count; i++) {
    users.push({
      id: `user_${i + 1}`,
      username: faker.internet.userName(),
      email: faker.internet.email(),
      name: faker.person.fullName(),
      avatar: faker.image.avatar(),
      role: randomChoice(roles),
      status: randomChoice(statuses),
      lastLoginAt: faker.date.recent().toISOString(),
      createdAt: faker.date.past().toISOString(),
      updatedAt: faker.date.recent().toISOString()
    })
  }
  
  return users
}

// Generate schools
export const generateSchools = (count = 50) => {
  const schools = []
  const types = ['university', 'college', 'institute', 'academy']
  const levels = ['undergraduate', 'graduate', 'mixed']
  const statuses = ['active', 'inactive', 'pending']
  
  for (let i = 0; i < count; i++) {
    const studentCount = faker.number.int({ min: 500, max: 50000 })
    
    schools.push({
      id: `school_${i + 1}`,
      name: faker.company.name() + ' University',
      shortName: faker.company.buzzAdjective().toUpperCase() + 'U',
      type: randomChoice(types),
      level: randomChoice(levels),
      status: randomChoice(statuses),
      country: faker.location.country(),
      province: faker.location.state(),
      city: faker.location.city(),
      address: faker.location.streetAddress(),
      website: faker.internet.url(),
      email: faker.internet.email(),
      phone: faker.phone.number(),
      establishedYear: faker.number.int({ min: 1800, max: 2020 }),
      studentCount,
      facultyCount: Math.floor(studentCount / 20),
      registeredAt: faker.date.past().toISOString(),
      lastActiveAt: faker.date.recent().toISOString(),
      isVerified: faker.datatype.boolean(),
      isPartner: faker.datatype.boolean(),
      logo: faker.image.url(),
      description: faker.lorem.paragraph(),
      tags: randomChoices(['engineering', 'science', 'business', 'arts'], 2),
      stats: {
        totalStudents: studentCount,
        activeStudents: Math.floor(studentCount * 0.7),
        totalCompetitions: faker.number.int({ min: 10, max: 100 }),
        totalSubmissions: faker.number.int({ min: 50, max: 500 }),
        totalCreditsEarned: faker.number.int({ min: 1000, max: 50000 }),
        averageScore: faker.number.float({ min: 60, max: 95, precision: 0.1 }),
        participationRate: faker.number.float({ min: 0.1, max: 0.8, precision: 0.01 }),
        completionRate: faker.number.float({ min: 0.5, max: 0.9, precision: 0.01 }),
        winRate: faker.number.float({ min: 0.05, max: 0.3, precision: 0.01 })
      },
      createdAt: faker.date.past().toISOString(),
      updatedAt: faker.date.recent().toISOString()
    })
  }
  
  return schools
}

// Generate competitions
export const generateCompetitions = (count = 30, users = [], schools = []) => {
  const competitions = []
  const types = ['hackathon', 'contest', 'challenge', 'tournament', 'workshop']
  const statuses = ['draft', 'published', 'registration_open', 'registration_closed', 'active', 'completed']
  const categories = ['programming', 'data_science', 'ai_ml', 'web_development', 'mobile', 'design']
  const difficulties = ['beginner', 'intermediate', 'advanced', 'expert']
  
  for (let i = 0; i < count; i++) {
    const startDate = faker.date.future()
    const endDate = new Date(startDate.getTime() + (3 * 24 * 60 * 60 * 1000)) // 3 days later
    const regStartDate = new Date(startDate.getTime() - (30 * 24 * 60 * 60 * 1000)) // 30 days before
    const regEndDate = new Date(startDate.getTime() - (7 * 24 * 60 * 60 * 1000)) // 7 days before
    
    const maxParticipants = faker.number.int({ min: 50, max: 1000 })
    const currentParticipants = faker.number.int({ min: 10, max: maxParticipants })
    
    competitions.push({
      id: `comp_${i + 1}`,
      title: faker.company.catchPhrase() + ' Challenge',
      description: faker.lorem.paragraphs(2),
      type: randomChoice(types),
      status: randomChoice(statuses),
      category: randomChoice(categories),
      difficulty: randomChoice(difficulties),
      registrationStartDate: regStartDate.toISOString(),
      registrationEndDate: regEndDate.toISOString(),
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      maxParticipants,
      currentParticipants,
      teamSize: {
        min: 1,
        max: faker.number.int({ min: 2, max: 5 })
      },
      rewards: [
        {
          rank: 1,
          title: 'First Place',
          description: 'Winner of the competition',
          credits: faker.number.int({ min: 500, max: 2000 }),
          cash: faker.number.int({ min: 1000, max: 10000 })
        },
        {
          rank: 2,
          title: 'Second Place',
          description: 'Runner-up',
          credits: faker.number.int({ min: 300, max: 1000 }),
          cash: faker.number.int({ min: 500, max: 5000 })
        }
      ],
      organizer: users.length > 0 ? randomChoice(users) : {
        id: 'user_1',
        name: faker.person.fullName(),
        email: faker.internet.email()
      },
      rules: faker.lorem.paragraphs(3),
      requirements: [
        'Must be a registered student',
        'Original work only',
        'Team collaboration allowed'
      ],
      tags: randomChoices(['ai', 'web', 'mobile', 'data', 'innovation'], 3),
      stats: {
        totalParticipants: currentParticipants,
        totalTeams: Math.floor(currentParticipants / 2),
        totalSubmissions: Math.floor(currentParticipants * 0.8),
        averageScore: faker.number.float({ min: 60, max: 90, precision: 0.1 }),
        completionRate: faker.number.float({ min: 0.6, max: 0.9, precision: 0.01 }),
        participantsBySchool: schools.slice(0, 10).reduce((acc, school) => {
          acc[school.name] = faker.number.int({ min: 1, max: 20 })
          return acc
        }, {}),
        participantsByLevel: {
          'beginner': faker.number.int({ min: 10, max: 50 }),
          'intermediate': faker.number.int({ min: 20, max: 80 }),
          'advanced': faker.number.int({ min: 5, max: 30 })
        }
      },
      isPublic: faker.datatype.boolean(),
      allowTeams: faker.datatype.boolean(),
      requireApproval: faker.datatype.boolean(),
      featured: faker.datatype.boolean(),
      slug: faker.lorem.slug(),
      viewCount: faker.number.int({ min: 100, max: 5000 }),
      createdAt: faker.date.past().toISOString(),
      updatedAt: faker.date.recent().toISOString()
    })
  }
  
  return competitions
}

// Generate credits
export const generateCredits = (count = 200, users = []) => {
  const credits = []
  const types = ['reward', 'bonus', 'penalty', 'adjustment', 'refund']
  const sources = ['competition', 'manual', 'system', 'promotion', 'referral']
  const statuses = ['pending', 'approved', 'issued', 'expired', 'cancelled']
  
  for (let i = 0; i < count; i++) {
    const user = users.length > 0 ? randomChoice(users) : { id: 'user_1', name: 'Test User' }
    const issuedBy = users.length > 0 ? randomChoice(users) : { id: 'user_admin', name: 'Admin User' }
    
    credits.push({
      id: `credit_${i + 1}`,
      amount: faker.number.int({ min: 10, max: 1000 }),
      type: randomChoice(types),
      source: randomChoice(sources),
      status: randomChoice(statuses),
      userId: user.id,
      user,
      reason: faker.lorem.sentence(),
      description: faker.lorem.paragraph(),
      reference: {
        type: 'competition',
        id: `comp_${faker.number.int({ min: 1, max: 30 })}`,
        title: faker.company.catchPhrase()
      },
      issuedBy,
      issuedAt: faker.date.past().toISOString(),
      expiresAt: faker.date.future().toISOString(),
      auditLog: [
        {
          id: `audit_${i + 1}_1`,
          action: 'created',
          performedBy: issuedBy,
          performedAt: faker.date.past().toISOString(),
          details: {},
          comment: 'Credit created'
        }
      ],
      createdAt: faker.date.past().toISOString(),
      updatedAt: faker.date.recent().toISOString()
    })
  }
  
  return credits
}

// Initialize all mock data
export const initializeMockData = () => {
  const users = generateUsers(100)
  const schools = generateSchools(50)
  const competitions = generateCompetitions(30, users, schools)
  const credits = generateCredits(200, users)
  
  return {
    users,
    schools,
    competitions,
    credits
  }
}
