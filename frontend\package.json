{"name": "community-services-frontend", "version": "1.0.0", "type": "module", "description": "Vue 3 frontend for community services administrative system", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "dayjs": "^1.11.10", "element-plus": "^2.4.4", "js-cookie": "^3.0.5", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "devDependencies": {"@faker-js/faker": "^9.8.0", "@playwright/test": "^1.53.0", "@vitejs/plugin-vue": "^5.2.4", "@vitest/coverage-v8": "^3.2.3", "@vitest/ui": "^3.2.3", "@vue/test-utils": "^2.4.3", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "jsdom": "^23.0.1", "prettier": "^3.1.1", "sass": "^1.69.5", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^6.3.5", "vitest": "^3.2.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}