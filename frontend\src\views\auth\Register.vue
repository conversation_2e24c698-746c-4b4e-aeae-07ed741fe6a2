<template>
  <div class="register-container">
    <div class="register-card">
      <div class="register-header">
        <h1 class="register-title">Create Account</h1>
        <p class="register-subtitle">Join the Community Services Admin</p>
      </div>

      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        class="register-form"
        @submit.prevent="handleRegister"
      >
        <el-form-item prop="name">
          <el-input
            v-model="registerForm.name"
            placeholder="Full name"
            size="large"
            :prefix-icon="User"
            :disabled="authStore.loading"
          />
        </el-form-item>

        <el-form-item prop="email">
          <el-input
            v-model="registerForm.email"
            type="email"
            placeholder="Email address"
            size="large"
            :prefix-icon="Message"
            :disabled="authStore.loading"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="Password"
            size="large"
            :prefix-icon="Lock"
            :disabled="authStore.loading"
            show-password
          />
        </el-form-item>

        <el-form-item prop="confirmPassword">
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="Confirm password"
            size="large"
            :prefix-icon="Lock"
            :disabled="authStore.loading"
            show-password
            @keyup.enter="handleRegister"
          />
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="registerForm.agreeToTerms">
            I agree to the
            <a href="#" class="terms-link">Terms of Service</a>
            and
            <a href="#" class="terms-link">Privacy Policy</a>
          </el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="register-button"
            :loading="authStore.loading"
            @click="handleRegister"
          >
            Create Account
          </el-button>
        </el-form-item>

        <div v-if="authStore.error" class="error-message">
          <el-alert
            :title="authStore.error"
            type="error"
            :closable="false"
            show-icon
          />
        </div>
      </el-form>

      <div class="register-footer">
        <p>
          Already have an account?
          <router-link to="/login" class="login-link">
            Sign in here
          </router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { User, Message, Lock } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()

// Form ref
const registerFormRef = ref()

// Form data
const registerForm = reactive({
  name: '',
  email: '',
  password: '',
  confirmPassword: '',
  agreeToTerms: false
})

// Custom validator for password confirmation
const validatePasswordConfirm = (rule, value, callback) => {
  if (value !== registerForm.password) {
    callback(new Error('Passwords do not match'))
  } else {
    callback()
  }
}

// Custom validator for terms agreement
const validateTerms = (rule, value, callback) => {
  if (!value) {
    callback(new Error('You must agree to the terms and conditions'))
  } else {
    callback()
  }
}

// Form validation rules
const registerRules = {
  name: [
    { required: true, message: 'Please enter your full name', trigger: 'blur' },
    { min: 2, message: 'Name must be at least 2 characters', trigger: 'blur' }
  ],
  email: [
    { required: true, message: 'Please enter your email address', trigger: 'blur' },
    { type: 'email', message: 'Please enter a valid email address', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'Please enter your password', trigger: 'blur' },
    { min: 8, message: 'Password must be at least 8 characters', trigger: 'blur' },
    {
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      message: 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: 'Please confirm your password', trigger: 'blur' },
    { validator: validatePasswordConfirm, trigger: 'blur' }
  ],
  agreeToTerms: [
    { validator: validateTerms, trigger: 'change' }
  ]
}

// Methods
const handleRegister = async () => {
  if (!registerFormRef.value) return

  try {
    const valid = await registerFormRef.value.validate()
    if (!valid) return

    const success = await authStore.register({
      name: registerForm.name,
      email: registerForm.email,
      password: registerForm.password,
      confirmPassword: registerForm.confirmPassword
    })

    if (success) {
      ElMessage.success('Registration successful! Please log in.')
      router.push('/login')
    }
  } catch (error) {
    console.error('Registration error:', error)
  }
}

onMounted(() => {
  // Clear any existing errors
  authStore.error = null
})
</script>

<style lang="scss" scoped>
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.register-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 450px;
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
  
  .register-title {
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 8px;
  }
  
  .register-subtitle {
    color: #7f8c8d;
    font-size: 16px;
  }
}

.register-form {
  .el-form-item {
    margin-bottom: 20px;
  }
  
  .register-button {
    width: 100%;
    height: 48px;
    font-size: 16px;
    font-weight: 600;
  }
  
  .terms-link {
    color: #409eff;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.error-message {
  margin-top: 16px;
}

.register-footer {
  text-align: center;
  margin-top: 24px;
  
  p {
    color: #7f8c8d;
    font-size: 14px;
  }
  
  .login-link {
    color: #409eff;
    text-decoration: none;
    font-weight: 500;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

// Responsive design
@media (max-width: 480px) {
  .register-card {
    padding: 30px 20px;
  }
  
  .register-header .register-title {
    font-size: 24px;
  }
}
</style>
