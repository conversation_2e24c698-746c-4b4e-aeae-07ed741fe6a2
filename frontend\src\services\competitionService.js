/**
 * Competition Service
 * Integrates with Camp Admin API and Analytics API for competition data
 * Replaces mock competitionService with real API calls
 */

import { competitionsApi } from './api/competitionsApi.js'
import { campAdminApi } from './api/campAdminApi.js'
import { analyticsApi } from './api/analyticsApi.js'
import { competitionAdapter } from './adapters/competitionAdapter.js'
import { analyticsAdapter } from './adapters/analyticsAdapter.js'
import { handleApiError, logApiCall, isMockApiEnabled } from '../utils/apiHelpers.js'

// Import mock service as fallback
import { competitionService as mockCompetitionService } from './mock/competitionService.js'

/**
 * Real Competition Service Implementation
 * Maps competition data from Camp Admin API to competition interface
 */
class CompetitionService {
  /**
   * Get competitions list
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number
   * @param {number} params.limit - Items per page
   * @param {string} params.status - Filter by status
   * @param {string} params.type - Filter by type
   * @param {string} params.category - Filter by category
   * @param {string} params.difficulty - Filter by difficulty
   * @param {string} params.search - Search term
   * @param {string} params.routeId - Filter by route ID
   * @param {boolean} params.featured - Filter featured competitions
   * @param {string} params.sortBy - Sort field
   * @param {string} params.sortOrder - Sort order (asc/desc)
   * @returns {Promise<Object>} Competitions list response
   */
  async getCompetitions(params = {}) {
    // Use mock service if enabled
    if (isMockApiEnabled()) {
      logApiCall('GET', '/competitions (mock)', params, null)
      return mockCompetitionService.getCompetitions(params)
    }

    try {
      logApiCall('GET', '/camp-admin/competitions', params, null)

      // Transform parameters for Camp Admin API
      const apiParams = {
        limit: params.limit || 20,
        offset: params.page ? (params.page - 1) * (params.limit || 20) : 0,
        route_id: params.routeId,
        status: params.status
      }

      // Get competitions from Camp Admin API
      const competitionsResponse = await competitionsApi.getCompetitions(apiParams)

      if (!competitionsResponse.success) {
        throw new Error(competitionsResponse.error?.message || 'Failed to fetch competitions')
      }

      // Transform competitions to frontend format
      const transformedData = competitionAdapter.transform(competitionsResponse, {
        type: 'list',
        isPaginated: false // Camp Admin API doesn't return pagination info
      })

      // Apply additional client-side filtering
      let competitions = transformedData.data
      
      // Apply filters not supported by API
      if (params.type) {
        competitions = competitions.filter(comp => comp.type === params.type)
      }
      
      if (params.category) {
        competitions = competitions.filter(comp => comp.category === params.category)
      }
      
      if (params.difficulty) {
        competitions = competitions.filter(comp => comp.difficulty === params.difficulty)
      }
      
      if (params.search) {
        const searchLower = params.search.toLowerCase()
        competitions = competitions.filter(comp => 
          comp.title.toLowerCase().includes(searchLower) ||
          comp.name.toLowerCase().includes(searchLower) ||
          (comp.description && comp.description.toLowerCase().includes(searchLower))
        )
      }
      
      if (params.featured !== undefined) {
        competitions = competitions.filter(comp => comp.featured === params.featured)
      }

      // Apply sorting
      if (params.sortBy) {
        competitions = this.sortCompetitions(competitions, params.sortBy, params.sortOrder)
      }

      // Enhance with statistics if available
      const enhancedCompetitions = await this.enhanceCompetitionsWithStats(competitions)

      // Create pagination info (client-side)
      const total = enhancedCompetitions.length
      const page = params.page || 1
      const limit = params.limit || 20
      const offset = (page - 1) * limit
      const paginatedData = enhancedCompetitions.slice(offset, offset + limit)

      const result = {
        success: true,
        data: paginatedData,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: total,
          pages: Math.ceil(total / limit),
          hasNext: offset + limit < total,
          hasPrev: page > 1
        }
      }

      logApiCall('GET', '/camp-admin/competitions', params, result)
      return result

    } catch (error) {
      const normalizedError = handleApiError(error)
      logApiCall('GET', '/camp-admin/competitions', params, normalizedError)
      throw normalizedError
    }
  }

  /**
   * Get competition by ID
   * @param {string} id - Competition ID
   * @returns {Promise<Object>} Competition details response
   */
  async getCompetition(id) {
    // Use mock service if enabled
    if (isMockApiEnabled()) {
      logApiCall('GET', `/competitions/${id} (mock)`, null, null)
      return mockCompetitionService.getCompetition(id)
    }

    try {
      logApiCall('GET', `/camp-admin/competitions/${id}`, null, null)

      // Get all competitions and find by ID (Camp Admin API doesn't have single competition endpoint)
      const competitionsResponse = await competitionsApi.getCompetitions({ limit: 1000 })

      if (!competitionsResponse.success) {
        throw new Error(competitionsResponse.error?.message || 'Failed to fetch competition')
      }

      const competition = competitionsResponse.data.find(c => 
        c.id === id || c.competition_id === id
      )

      if (!competition) {
        return {
          success: false,
          error: {
            code: 'COMPETITION_NOT_FOUND',
            message: 'Competition not found'
          }
        }
      }

      // Transform to detailed competition format
      const transformedCompetition = competitionAdapter.transformItem(competition, 'detail')

      // Enhance with statistics and qualifications
      const competitionWithStats = await this.enhanceCompetitionWithStats(transformedCompetition)
      const competitionWithQualifications = await this.enhanceCompetitionWithQualifications(competitionWithStats)

      const result = {
        success: true,
        data: competitionWithQualifications
      }

      logApiCall('GET', `/camp-admin/competitions/${id}`, null, result)
      return result

    } catch (error) {
      const normalizedError = handleApiError(error)
      logApiCall('GET', `/camp-admin/competitions/${id}`, null, normalizedError)
      throw normalizedError
    }
  }

  /**
   * Get competition statistics
   * @returns {Promise<Object>} Competition statistics response
   */
  async getCompetitionStats() {
    // Use mock service if enabled
    if (isMockApiEnabled()) {
      logApiCall('GET', '/competitions/stats (mock)', null, null)
      return mockCompetitionService.getCompetitionStats()
    }

    try {
      logApiCall('GET', '/competitions/stats', null, null)

      // Get data from multiple sources
      const [dashboardResponse, summaryStatsResponse, routesResponse] = await Promise.all([
        competitionsApi.getCompetitionsDashboardData(),
        analyticsApi.getSummaryStatistics(),
        competitionsApi.getRoutes({ limit: 100 })
      ])

      if (!dashboardResponse.success) {
        throw new Error('Failed to fetch dashboard data')
      }

      // Transform analytics data
      const summaryStats = analyticsAdapter.transformSummaryStatistics(summaryStatsResponse.data || [])
      const competitions = dashboardResponse.data.recent_competitions || []
      const routes = dashboardResponse.data.available_routes || []

      // Calculate statistics
      const totalCompetitions = dashboardResponse.data.total_competitions || competitions.length
      const activeCompetitions = competitions.filter(c => c.status === 'active').length
      const upcomingCompetitions = competitions.filter(c => c.status === 'upcoming').length
      const completedCompetitions = competitions.filter(c => c.status === 'completed').length

      // Route distribution
      const routeDistribution = routes.reduce((acc, route) => {
        acc[route.name] = route.competition_count || 0
        return acc
      }, {})

      // Status distribution
      const statusDistribution = competitions.reduce((acc, comp) => {
        const status = comp.status || 'unknown'
        acc[status] = (acc[status] || 0) + 1
        return acc
      }, {})

      const stats = {
        totalCompetitions,
        activeCompetitions,
        upcomingCompetitions,
        completedCompetitions,
        totalParticipants: summaryStats.totalParticipants || 0,
        totalSubmissions: summaryStats.totalSubmissions || 0,
        averageParticipants: totalCompetitions > 0 ? Math.round((summaryStats.totalParticipants || 0) / totalCompetitions) : 0,
        routeDistribution,
        statusDistribution,
        recentCompetitions: competitions.slice(0, 10),
        availableRoutes: routes,
        summary: summaryStats
      }

      const result = {
        success: true,
        data: stats
      }

      logApiCall('GET', '/competitions/stats', null, result)
      return result

    } catch (error) {
      const normalizedError = handleApiError(error)
      logApiCall('GET', '/competitions/stats', null, normalizedError)
      throw normalizedError
    }
  }

  /**
   * Get competition routes
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Routes list response
   */
  async getRoutes(params = {}) {
    // Use mock service if enabled
    if (isMockApiEnabled()) {
      logApiCall('GET', '/routes (mock)', params, null)
      // Mock doesn't have routes, return empty
      return { success: true, data: [] }
    }

    try {
      logApiCall('GET', '/camp-admin/routes', params, null)

      const routesResponse = await competitionsApi.getRoutes(params)

      if (!routesResponse.success) {
        throw new Error(routesResponse.error?.message || 'Failed to fetch routes')
      }

      // Transform routes
      const transformedData = competitionAdapter.transformRoutes(routesResponse, {
        type: 'list'
      })

      logApiCall('GET', '/camp-admin/routes', params, transformedData)
      return transformedData

    } catch (error) {
      const normalizedError = handleApiError(error)
      logApiCall('GET', '/camp-admin/routes', params, normalizedError)
      throw normalizedError
    }
  }

  /**
   * Create competition (not supported by Camp Admin API)
   * @param {Object} competitionData - Competition data
   * @returns {Promise<Object>} Create response
   */
  async createCompetition(competitionData) {
    // Always use mock for create operations
    logApiCall('POST', '/competitions (mock)', competitionData, null)
    return mockCompetitionService.createCompetition(competitionData)
  }

  /**
   * Update competition (not supported by Camp Admin API)
   * @param {string} id - Competition ID
   * @param {Object} competitionData - Competition data
   * @returns {Promise<Object>} Update response
   */
  async updateCompetition(id, competitionData) {
    // Always use mock for update operations
    logApiCall('PUT', `/competitions/${id} (mock)`, competitionData, null)
    return mockCompetitionService.updateCompetition(id, competitionData)
  }

  /**
   * Delete competition (not supported by Camp Admin API)
   * @param {string} id - Competition ID
   * @returns {Promise<Object>} Delete response
   */
  async deleteCompetition(id) {
    // Always use mock for delete operations
    logApiCall('DELETE', `/competitions/${id} (mock)`, null, null)
    return mockCompetitionService.deleteCompetition(id)
  }

  /**
   * Join competition (not supported by Camp Admin API)
   * @param {string} competitionId - Competition ID
   * @param {Object} joinData - Join data
   * @returns {Promise<Object>} Join response
   */
  async joinCompetition(competitionId, joinData) {
    // Always use mock for join operations
    logApiCall('POST', `/competitions/${competitionId}/join (mock)`, joinData, null)
    return mockCompetitionService.joinCompetition(competitionId, joinData)
  }

  /**
   * Sort competitions array
   * @param {Array} competitions - Competitions array
   * @param {string} sortBy - Sort field
   * @param {string} sortOrder - Sort order
   * @returns {Array} Sorted competitions
   */
  sortCompetitions(competitions, sortBy, sortOrder = 'asc') {
    return competitions.sort((a, b) => {
      let aVal = a[sortBy]
      let bVal = b[sortBy]

      // Handle nested properties
      if (sortBy === 'participants' || sortBy === 'submissions') {
        aVal = a.stats?.[sortBy] || 0
        bVal = b.stats?.[sortBy] || 0
      }

      // Handle dates
      if (sortBy === 'startDate' || sortBy === 'endDate' || sortBy === 'createdAt') {
        aVal = new Date(aVal || 0)
        bVal = new Date(bVal || 0)
      }

      // Handle string comparison
      if (typeof aVal === 'string') {
        aVal = aVal.toLowerCase()
        bVal = bVal.toLowerCase()
      }

      // Sort logic
      if (sortOrder === 'desc') {
        return bVal > aVal ? 1 : -1
      }
      return aVal > bVal ? 1 : -1
    })
  }

  /**
   * Enhance competitions with statistics
   * @param {Array} competitions - Competitions array
   * @returns {Promise<Array>} Enhanced competitions
   */
  async enhanceCompetitionsWithStats(competitions) {
    // For now, return competitions as-is
    // In the future, could fetch individual stats for each competition
    return competitions.map(competition => ({
      ...competition,
      stats: competition.stats || {
        totalParticipants: 0,
        totalSubmissions: 0,
        averageScore: 0,
        completionRate: 0
      }
    }))
  }

  /**
   * Enhance single competition with statistics
   * @param {Object} competition - Competition object
   * @returns {Promise<Object>} Enhanced competition
   */
  async enhanceCompetitionWithStats(competition) {
    try {
      // Try to get competition-specific stats
      const stats = await competitionsApi.getCompetitionStats(competition.id)
      
      return {
        ...competition,
        stats: stats.success ? stats.data : competition.stats || {}
      }
    } catch (error) {
      // Return competition with default stats if enhancement fails
      return {
        ...competition,
        stats: competition.stats || {
          totalParticipants: 0,
          totalSubmissions: 0,
          averageScore: 0,
          completionRate: 0
        }
      }
    }
  }

  /**
   * Enhance competition with qualifications
   * @param {Object} competition - Competition object
   * @returns {Promise<Object>} Enhanced competition
   */
  async enhanceCompetitionWithQualifications(competition) {
    try {
      // Get qualifications for this competition
      const qualificationsResponse = await competitionsApi.getCompetitionQualifications(competition.id)
      
      return {
        ...competition,
        qualifications: qualificationsResponse.success ? qualificationsResponse.data : []
      }
    } catch (error) {
      // Return competition without qualifications if enhancement fails
      return {
        ...competition,
        qualifications: []
      }
    }
  }
}

// Create and export service instance
export const competitionService = new CompetitionService()

// Export individual methods for convenience
export const {
  getCompetitions,
  getCompetition,
  getCompetitionStats,
  getRoutes,
  createCompetition,
  updateCompetition,
  deleteCompetition,
  joinCompetition
} = competitionService
