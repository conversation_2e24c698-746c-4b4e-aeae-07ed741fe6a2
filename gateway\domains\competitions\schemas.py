"""
Pydantic schemas for competition management endpoints.
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from enum import Enum


# ——— Migrated from shared/schemas/api/job.py ———


class CompetitionUserRegisterRequest(BaseModel):
    """Request schema for fetching competition users by registration date."""

    register_start_date: Optional[str] = Field(None, description="注册开始时间")


class CompetitionUserIdentityRegisterRequest(BaseModel):
    """Request schema for fetching competition users by identity."""

    identity: str
    register_start_date: Optional[str] = Field(None, description="注册开始时间")
    

class CompetitionUserProfileType(str, Enum):
    """Types of competition user profile."""
    BASIC = "basic"
    DETAIL = "detail"
    FULL_FORM = "full_form"
    
class CompetitionUserProfileBasic(BaseModel):
    
    user_id: str = Field(..., description="用户ID")
    register_ts: str = Field(alias="register_ts", description="注册时间")
    Phone: Optional[str] = Field(None, alias="Phone", description="手机号")
    Email: Optional[str] = Field(None, alias="Email", description="邮箱")
    competition_id: str = Field(alias="competition_id", description="比赛ID")
    
class CompetitionUserProfileDetail(CompetitionUserProfileBasic):
    competition_type: Optional[str] = Field(..., alias="competition_type", description="比赛类型")
    competition_name: Optional[str] = Field(..., alias="competition_name", description="比赛名称")
    
class CompetitionUserProfileFullForm(CompetitionUserProfileBasic):
    register_form: Dict[str, Any] = Field(alias="RegisterForm", description="注册表单")
    

class CompetitionUserProfileResponse(BaseModel):
    """Response schema for competition user profile."""
    data: List[CompetitionUserProfileBasic] = Field(..., description="List of competition user profiles")
    total: int = Field(..., description="Total number of competition user profiles")
    limit: int = Field(..., description="Limit of competition user profiles per page")
    offset: int = Field(..., description="Offset of competition user profiles")